#!/usr/bin/env python3
"""
创建一个测试XMind文件来验证修复是否有效
"""
from services.xmind_service import xmind_service

def create_test_xmind():
    """创建一个包含完整测试用例的XMind文件"""
    
    test_cases = [
        {
            'id': 'TC-001',
            'title': '用户登录功能测试',
            'description': '验证用户能够使用正确的用户名和密码成功登录系统',
            'priority': 'High',
            'preconditions': '用户账户已创建且处于激活状态',
            'steps': [
                {
                    'step_number': 1,
                    'description': '打开登录页面',
                    'expected_result': '登录页面正确显示，包含用户名和密码输入框'
                },
                {
                    'step_number': 2,
                    'description': '输入有效的用户名和密码',
                    'expected_result': '用户名和密码输入框接受输入'
                },
                {
                    'step_number': 3,
                    'description': '点击登录按钮',
                    'expected_result': '系统验证凭据并重定向到主页面'
                }
            ]
        },
        {
            'id': 'TC-002',
            'title': '用户登录失败测试',
            'description': '验证使用错误凭据时登录失败的处理',
            'priority': 'Medium',
            'preconditions': '登录页面可访问',
            'steps': [
                {
                    'step_number': 1,
                    'description': '打开登录页面',
                    'expected_result': '登录页面正确显示'
                },
                {
                    'step_number': 2,
                    'description': '输入错误的用户名或密码',
                    'expected_result': '输入框接受输入'
                },
                {
                    'step_number': 3,
                    'description': '点击登录按钮',
                    'expected_result': '显示错误消息，用户仍在登录页面'
                }
            ]
        },
        {
            'id': 'TC-003',
            'title': '密码重置功能测试',
            'description': '验证用户能够重置忘记的密码',
            'priority': 'Low',
            'preconditions': '用户邮箱地址有效且可接收邮件',
            'steps': [
                {
                    'step_number': 1,
                    'description': '点击"忘记密码"链接',
                    'expected_result': '跳转到密码重置页面'
                },
                {
                    'step_number': 2,
                    'description': '输入注册邮箱地址',
                    'expected_result': '邮箱输入框接受有效邮箱格式'
                },
                {
                    'step_number': 3,
                    'description': '点击发送重置邮件按钮',
                    'expected_result': '系统发送重置邮件并显示确认消息'
                },
                {
                    'step_number': 4,
                    'description': '检查邮箱并点击重置链接',
                    'expected_result': '跳转到新密码设置页面'
                }
            ]
        }
    ]
    
    # 生成XMind文件
    filepath = xmind_service.generate_xmind(test_cases, "完整测试用例")
    
    print(f"✓ 已生成测试XMind文件: {filepath}")
    print("请尝试用XMind软件打开此文件验证是否正常工作")
    
    return filepath

if __name__ == "__main__":
    create_test_xmind()
