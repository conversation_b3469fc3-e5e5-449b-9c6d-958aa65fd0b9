#!/usr/bin/env python3
import os
import xmind
import zipfile
from services.xmind_service import xmind_service

def test_xmind_generation():
    """测试XMind文件生成和结构"""
    
    # 创建测试XMind文件
    test_file = "test_xmind_structure.xmind"
    
    # 创建XMind工作簿
    workbook = xmind.load(test_file)
    sheet = workbook.getPrimarySheet()
    sheet.setTitle("测试")
    
    # 创建根主题
    root_topic = sheet.getRootTopic()
    root_topic.setTitle("测试主题")
    
    # 添加子主题
    sub_topic = root_topic.addSubTopic()
    sub_topic.setTitle("子主题")
    
    # 保存文件
    xmind.save(workbook, test_file)
    
    print(f"生成的XMind文件: {test_file}")
    
    # 检查文件结构
    if os.path.exists(test_file):
        print(f"文件大小: {os.path.getsize(test_file)} bytes")
        
        # 检查ZIP内容
        with zipfile.ZipFile(test_file, 'r') as zip_file:
            file_list = zip_file.namelist()
            print("ZIP文件内容:")
            for file_name in file_list:
                print(f"  - {file_name}")
            
            # 检查是否有META-INF/manifest.xml
            if 'META-INF/manifest.xml' in file_list:
                print("✓ 包含 META-INF/manifest.xml")
                # 读取manifest.xml内容
                manifest_content = zip_file.read('META-INF/manifest.xml').decode('utf-8')
                print("manifest.xml内容:")
                print(manifest_content)
            else:
                print("✗ 缺少 META-INF/manifest.xml")
                
            # 检查content.xml
            if 'content.xml' in file_list:
                print("✓ 包含 content.xml")
            else:
                print("✗ 缺少 content.xml")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)

def test_xmind_service():
    """测试XMind服务生成的文件"""

    # 创建测试数据
    test_cases = [
        {
            'id': 'TC-001',
            'title': '测试用例1',
            'description': '这是一个测试用例',
            'priority': 'High',
            'preconditions': '测试环境已准备',
            'steps': [
                {
                    'step_number': 1,
                    'description': '执行步骤1',
                    'expected_result': '预期结果1'
                },
                {
                    'step_number': 2,
                    'description': '执行步骤2',
                    'expected_result': '预期结果2'
                }
            ]
        }
    ]

    # 使用XMind服务生成文件
    filepath = xmind_service.generate_xmind(test_cases, "test_service")

    print(f"XMind服务生成的文件: {filepath}")

    # 检查文件结构
    if os.path.exists(filepath):
        print(f"文件大小: {os.path.getsize(filepath)} bytes")

        # 检查ZIP内容
        with zipfile.ZipFile(filepath, 'r') as zip_file:
            file_list = zip_file.namelist()
            print("ZIP文件内容:")
            for file_name in file_list:
                print(f"  - {file_name}")

            # 检查是否有META-INF/manifest.xml
            if 'META-INF/manifest.xml' in file_list:
                print("✓ 包含 META-INF/manifest.xml")
                # 读取manifest.xml内容
                manifest_content = zip_file.read('META-INF/manifest.xml').decode('utf-8')
                print("manifest.xml内容:")
                print(manifest_content)
            else:
                print("✗ 缺少 META-INF/manifest.xml")

    # 清理测试文件
    if os.path.exists(filepath):
        os.remove(filepath)

if __name__ == "__main__":
    print("=== 测试原始xmind库 ===")
    test_xmind_generation()
    print("\n=== 测试修复后的XMind服务 ===")
    test_xmind_service()
